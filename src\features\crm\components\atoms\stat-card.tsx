import React from "react";
import { Card, ConfigProvider, <PERSON>atistic, Tooltip } from "antd";
import { InfoIcon } from "lucide-react";

interface StatCardProps {
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color: string;
    prefix?: React.ReactNode;
    suffix?: React.ReactNode;
    info?: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({
    title,
    value,
    icon,
    color,
    prefix,
    suffix,
    info,
}) => {
    return (
        <Card className="shadow-md hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-center justify-between">
                <div>
                    <p className="text-sm text-gray-500 mb-1">{title}</p>
                    <Statistic
                        value={value}
                        prefix={prefix}
                        suffix={suffix}
                        valueStyle={{ color, fontWeight: "bold" }}
                    />
                </div>
                <div className="flex flex-col items-center">
                    {info && (
                        <ConfigProvider
                            theme={{
                                components: {
                                    Tooltip: {
                                        colorBgSpotlight: "rgba(255, 255, 255, 0.97)",
                                        colorTextLightSolid: "#1a1a1a",
                                        borderRadius: 12,
                                        boxShadowSecondary:
                                            "0 8px 24px rgba(24, 144, 255, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08)",
                                        paddingXS: 16,
                                        fontSize: 14,
                                        lineHeight: 1.5,
                                        colorBorder: "#1890ff",
                                    },
                                },
                            }}
                        >
                            <Tooltip title={info} placement="topLeft">
                                <InfoIcon size={14} className="text-gray-400" />
                            </Tooltip>
                        </ConfigProvider>
                    )}
                    <div className={`p-3 rounded-full bg-${color}/10`}>{icon}</div>
                </div>
            </div>
        </Card>
    );
};

export default StatCard;
