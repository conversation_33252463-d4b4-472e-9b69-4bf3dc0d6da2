import { <PERSON><PERSON>, <PERSON>, InputN<PERSON>ber, DatePicker, Switch, Tooltip } from "antd";
import {
    ExternalLink,
    Mail,
    Phone,
    Save,
    Trash,
    CheckCircle,
    XCircle,
    ToggleLeft,
    Calendar,
    Clock,
    Info,
    XOctagon,
} from "lucide-react";
import {
    PaymentCurrency,
    PaymentRetrieve,
    PaymentUpdateForm,
    PaymentUpdateRequest,
} from "../../types/payment";
import UploadVoucher from "../molecules/upload-payment-voucher";
import { Link } from "react-router-dom";
import SelectPaymentMethod from "../molecules/select-payment-method";
import RadioCurrency from "../molecules/radio-currency";
import { useUpdatePayment } from "../../hooks/use-update-payment";
import { useDeletePayment } from "../../hooks/use-delete-payment";
import { useTogglePaymentStatus } from "../../hooks/use-toggle-payment-status";
import { useTogglePaymentLost } from "../../hooks/use-toggle-payment-lost";
import dayjs from "dayjs";

type EditPaymentFormProps = {
    payment: PaymentRetrieve;
};

export default function EditPaymentForm({ payment }: EditPaymentFormProps) {
    const { order, voucher } = payment;
    const [form] = Form.useForm<PaymentUpdateForm>();

    const updatePaymentMutation = useUpdatePayment();
    const { deletePayment, isLoading: isDeleting } = useDeletePayment();
    const { togglePaymentStatus, isLoading: isTogglingStatus } =
        useTogglePaymentStatus();
    const { togglePaymentLost, isLoading: isTogglingLost } = useTogglePaymentLost(
        (newIsLost: boolean) => {
            // Update the form state when the toggle is successful
            form.setFieldValue("isLost", newIsLost);
        },
    );

    const currency = Form.useWatch(["currency"], form);
    const isLost = Form.useWatch(["isLost"], form) ?? payment.isLost; // Watch form value with fallback to payment value
    const isPaid = payment.isPaid; // Use the original payment value, not from form

    const handleSubmit = (values: PaymentUpdateForm) => {
        const updateData: PaymentUpdateRequest = {
            amount: values.amount,
            currency: values.currency,
            paymentMethod: values.paymentMethod,
            isFirstPayment: values.isFirstPayment,
            isLost: values.isLost,
            paymentDate: values.paymentDate
                ? dayjs(values.paymentDate).toISOString()
                : undefined,
            scheduledPaymentDate: values.scheduledPaymentDate
                ? dayjs(values.scheduledPaymentDate).toISOString()
                : undefined,
        };

        updatePaymentMutation.mutate({
            pid: payment.pid,
            payment: updateData,
        });
    };

    const handleDelete = () => {
        deletePayment(payment.pid);
    };

    return (
        <>
            <Form
                name="edit-order-form"
                layout="vertical"
                form={form}
                onFinish={handleSubmit}
                initialValues={{
                    amount: payment.amount,
                    paymentMethod: payment.paymentMethod?.pmid,
                    currency: payment.currency,
                    isFirstPayment: payment.isFirstPayment,
                    isLost: payment.isLost,
                    paymentDate: payment.paymentDate
                        ? dayjs(payment.paymentDate)
                        : undefined,
                    scheduledPaymentDate: payment.scheduledPaymentDate
                        ? dayjs(payment.scheduledPaymentDate)
                        : undefined,
                }}
            >
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                    <div className="col-span-4 space-y-6">
                        <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                            <p className="text-gray-400 font-semibold text-sm uppercase">
                                SOBRE LA ORDEN
                            </p>

                            <div className="px-1 rounded-md hover:bg-gray-50 transition-colors">
                                <div className="flex items-center gap-4">
                                    <p className="text-sm font-medium">
                                        {order.owner.fullName}{" "}
                                    </p>
                                    <Link
                                        to={`/crm/orders/${order.oid}`}
                                        className="flex items-center text-blue-600 hover:text-blue-800"
                                    >
                                        <span className="text-sm font-medium mr-1">
                                            #{order.oid.slice(-6)}
                                        </span>
                                        <ExternalLink size={14} />
                                    </Link>
                                </div>

                                <div className="flex gap-2">
                                    {order.owner.email && (
                                        <p className="text-xs text-gray-500 flex items-center mt-1">
                                            <Mail
                                                size={14}
                                                className="h-3.5 w-3.5 mr-1"
                                            />
                                            {order.owner.email}
                                        </p>
                                    )}

                                    <p className="text-xs text-gray-500 flex items-center mt-1">
                                        <Phone size={14} className="h-3.5 w-3.5 mr-1" />
                                        {order.owner.phoneNumber}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                            <div className="flex items-center justify-between">
                                <p className="text-gray-400 font-semibold text-sm">
                                    DETALLES DEL PAGO
                                </p>

                                <div className="flex items-center gap-3">
                                    <span
                                        className={`text-sm px-3 py-1 rounded-full flex items-center font-medium ${
                                            isPaid
                                                ? "bg-green-100 text-green-800 border border-green-300"
                                                : "bg-orange-100 text-orange-800 border border-red-300"
                                        }`}
                                    >
                                        {isPaid ? (
                                            <CheckCircle className="w-4 h-4 mr-1.5" />
                                        ) : (
                                            <XCircle className="w-4 h-4 mr-1.5" />
                                        )}
                                        {isPaid ? "Pagado" : "Pendiente"}
                                    </span>

                                    {isLost && (
                                        <span className="text-sm px-3 py-1 rounded-full bg-red-100 text-red-800 border border-red-300 font-medium flex items-center">
                                            <XOctagon className="w-4 h-4 mr-1.5" />
                                            Perdido
                                        </span>
                                    )}

                                    {payment.isFirstPayment && (
                                        <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 border border-blue-300 font-medium">
                                            Primer Pago
                                        </span>
                                    )}

                                    <Button
                                        type="default"
                                        onClick={() =>
                                            togglePaymentStatus(
                                                payment.pid,
                                                isPaid,
                                                !!voucher,
                                            )
                                        }
                                        loading={isTogglingStatus}
                                        className="flex items-center text-xs hover:border-blue-500 hover:text-blue-600"
                                        icon={
                                            <ToggleLeft className="w-3.5 h-3.5 mr-1" />
                                        }
                                        disabled={isLost} // Disable status toggle when payment is lost
                                    >
                                        {isPaid
                                            ? "Marcar como pendiente"
                                            : "Marcar como pagado"}
                                    </Button>

                                    <Button
                                        type="default"
                                        onClick={() =>
                                            togglePaymentLost(payment.pid, isLost)
                                        }
                                        loading={isTogglingLost}
                                        className={`flex items-center text-xs ${
                                            isLost
                                                ? "hover:border-green-500 hover:text-green-600"
                                                : "hover:border-red-500 hover:text-red-600"
                                        }`}
                                        icon={<XOctagon className="w-3.5 h-3.5 mr-1" />}
                                    >
                                        {isLost
                                            ? "Marcar como activo"
                                            : "Marcar como perdido"}
                                    </Button>
                                </div>
                            </div>
                            <div className="grid grid-cols-4 gap-y-2 gap-x-2">
                                <Form.Item<PaymentUpdateForm>
                                    name="paymentMethod"
                                    label="Método de Pago"
                                    className="col-span-2"
                                >
                                    <SelectPaymentMethod />
                                </Form.Item>
                                <Form.Item<PaymentUpdateForm>
                                    name="currency"
                                    label="Moneda"
                                    className="col-span-1"
                                >
                                    <RadioCurrency block />
                                </Form.Item>
                                <Form.Item<PaymentUpdateForm>
                                    name="amount"
                                    label="Monto"
                                    rules={[
                                        {
                                            required: true,
                                            message: "Por favor ingresa el monto",
                                        },
                                    ]}
                                    className="col-span-1"
                                >
                                    <InputNumber
                                        className="w-full"
                                        prefix={
                                            currency === PaymentCurrency.PEN
                                                ? "S/ "
                                                : "$ "
                                        }
                                    />
                                </Form.Item>

                                {/* Switches section */}
                                <Form.Item<PaymentUpdateForm>
                                    name="isFirstPayment"
                                    label={
                                        <span className="flex items-center gap-1">
                                            ¿Primer pago?
                                            <Tooltip title="Indica si este pago es para cerrar la venta (primer pago del cliente)">
                                                <Info
                                                    size={14}
                                                    className="text-gray-400 cursor-help"
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    className="col-span-2"
                                >
                                    <Switch />
                                </Form.Item>
                                <Form.Item<PaymentUpdateForm>
                                    name="isLost"
                                    label={
                                        <span className="flex items-center gap-1">
                                            ¿Pago perdido?
                                            <Tooltip title="Marca el pago como perdido - no se considerará en reportes de ingresos">
                                                <Info
                                                    size={14}
                                                    className="text-gray-400 cursor-help"
                                                />
                                            </Tooltip>
                                        </span>
                                    }
                                    className="col-span-2"
                                >
                                    <Switch />
                                </Form.Item>

                                {/* Divider for date section */}
                                <div className="col-span-4 border-t border-gray-100 my-2"></div>

                                {/* Date fields based on isPaid logic */}
                                {isPaid ? (
                                    // When payment is paid, show payment date and optionally scheduled date
                                    <>
                                        <Form.Item<PaymentUpdateForm>
                                            name="paymentDate"
                                            label={
                                                <span className="flex items-center gap-1">
                                                    <Calendar className="w-4 h-4 text-green-600" />
                                                    Fecha de Pago
                                                </span>
                                            }
                                            className="col-span-2"
                                        >
                                            <DatePicker
                                                className="w-full"
                                                placeholder="Seleccionar fecha de pago"
                                            />
                                        </Form.Item>

                                        {payment.scheduledPaymentDate && (
                                            <Form.Item<PaymentUpdateForm>
                                                name="scheduledPaymentDate"
                                                label={
                                                    <span className="flex items-center gap-1">
                                                        <Clock className="w-4 h-4 text-gray-500" />
                                                        Fecha Original
                                                    </span>
                                                }
                                                className="col-span-2"
                                            >
                                                <DatePicker
                                                    className="w-full"
                                                    placeholder="Fecha programada"
                                                    disabled
                                                />
                                            </Form.Item>
                                        )}
                                    </>
                                ) : (
                                    // When payment is not paid, show only scheduled payment date
                                    <Form.Item<PaymentUpdateForm>
                                        name="scheduledPaymentDate"
                                        label={
                                            <span className="flex items-center gap-1">
                                                <Clock className="w-4 h-4 text-orange-600" />
                                                Fecha Programada de Pago
                                            </span>
                                        }
                                        className="col-span-4"
                                    >
                                        <DatePicker
                                            className="w-full"
                                            placeholder="Seleccionar fecha programada"
                                        />
                                    </Form.Item>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="col-span-2 space-y-6">
                        <div className="bg-white-full p-5 rounded-lg shadow-sm">
                            <p className="text-gray-400 font-semibold text-sm">
                                ACCIONES
                            </p>
                            <div className="flex flex-col gap-3">
                                <div className="flex gap-3 justify-end">
                                    <Button
                                        type="primary"
                                        size="large"
                                        style={{ fontSize: 16 }}
                                        icon={<Trash />}
                                        danger
                                        loading={isDeleting}
                                        onClick={handleDelete}
                                    >
                                        Eliminar
                                    </Button>
                                    <Button
                                        type="primary"
                                        size="large"
                                        style={{ fontSize: 16 }}
                                        icon={<Save />}
                                        htmlType="submit"
                                        loading={updatePaymentMutation.isPending}
                                    >
                                        Guardar
                                    </Button>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white-full p-5 rounded-lg shadow-sm">
                            <p className="text-gray-400 font-semibold text-sm">
                                VOUCHER
                            </p>
                            <UploadVoucher pid={payment.pid} initialVoucher={voucher} />
                        </div>
                    </div>
                </div>
            </Form>
        </>
    );
}
