import { PaginatedResponse } from "@myTypes/base";
import { Major } from "@/features/crm/types/major";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 100;

export const getMajors = async (): Promise<PaginatedResponse<Major>> => {
    const response = await portalsApi.get("crm/majors", {
        params: {
            page: DEFAULT_PAGE,
            pageSize: DEFAULT_PAGE_SIZE,
        },
    });
    return response.data;
};

export const createMajor = async (name: string): Promise<Major> => {
    const response = await portalsApi.post("crm/majors", { name });
    return response.data;
};
