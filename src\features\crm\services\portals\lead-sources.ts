import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import { LeadSourceCreate, ListLeadSource } from "../../types/lead-source";

export const getLeadSources = async (): Promise<PaginatedResponse<ListLeadSource>> => {
    const response = await portalsApi.get("crm/lead-sources");
    return response.data;
};

export const createLeadSource = async (leadSource: LeadSourceCreate) => {
    const response = await portalsApi.post("crm/lead-sources", leadSource);
    return response.data;
};