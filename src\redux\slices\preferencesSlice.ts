import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { preferencesService } from "@services/preferences.service";

type PreferencesState = {
    currentApp: string;
};

const initialState: PreferencesState = {
    currentApp: preferencesService.getCurrentApp(),
};

const preferencesSlice = createSlice({
    name: "preferences",
    initialState,
    reducers: {
        setCurrentApp(state, action: PayloadAction<string>) {
            preferencesService.setCurrentApp(action.payload);
            state.currentApp = action.payload;
        },
    },
});

export type { PreferencesState };
export const { setCurrentApp } = preferencesSlice.actions;
export default preferencesSlice.reducer;
