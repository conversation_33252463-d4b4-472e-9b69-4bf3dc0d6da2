import React, { useState } from "react";
import { Card, Radio, Empty } from "antd";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { BarChart3 } from "lucide-react";
import type { EventByType, EventStats } from "@/features/crm/types/dashboard/events";

interface EventPieChartWithToggleProps {
    eventByTypeData?: EventByType;
    eventStatsData?: EventStats;
    isLoading?: boolean;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        color: string;
        name: string;
        value: string | number;
        dataKey: string;
    }>;
    label?: string;
}

interface CustomLabelProps {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
}

const COLORS = ["#1890ff", "#52c41a", "#faad14", "#fa541c", "#722ed1"];

type ChartMode = "eventType" | "enrollmentsByType" | "eventState";

const EventPieChartWithToggle: React.FC<EventPieChartWithToggleProps> = ({
    eventByTypeData,
    eventStatsData,
    isLoading = false,
}) => {
    const [chartMode, setChartMode] = useState<ChartMode>("eventType");

    const getChartData = () => {
        switch (chartMode) {
            case "eventType":
                return eventByTypeData
                    ? [
                          {
                              name: "General",
                              value: eventByTypeData.general.count,
                          },
                          {
                              name: "Específico",
                              value: eventByTypeData.specific.count,
                          },
                      ]
                    : [];
            case "enrollmentsByType":
                return eventByTypeData
                    ? [
                          {
                              name: "General",
                              value: eventByTypeData.general.totalEnrollments,
                          },
                          {
                              name: "Específico",
                              value: eventByTypeData.specific.totalEnrollments,
                          },
                      ]
                    : [];
            case "eventState":
                return eventStatsData
                    ? [
                          {
                              name: "En Planificación",
                              value: eventStatsData.planning,
                          },
                          {
                              name: "Lanzados",
                              value: eventStatsData.launched,
                          },
                          {
                              name: "Inscripciones Cerradas",
                              value: eventStatsData.enrollmentClosed,
                          },
                          {
                              name: "Finalizados",
                              value: eventStatsData.finished,
                          },
                      ]
                    : [];
            default:
                return [];
        }
    };

    const chartData = getChartData();

    const renderCustomizedLabel = ({
        cx,
        cy,
        midAngle,
        innerRadius,
        outerRadius,
        percent,
    }: CustomLabelProps) => {
        const RADIAN = Math.PI / 180;
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * RADIAN);
        const y = cy + radius * Math.sin(-midAngle * RADIAN);

        return (
            <text
                x={x}
                y={y}
                fill="white"
                textAnchor="middle"
                dominantBaseline="central"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        );
    };

    const getTooltipLabel = () => {
        switch (chartMode) {
            case "eventType":
                return "Eventos";
            case "enrollmentsByType":
                return "Inscripciones";
            case "eventState":
                return "Eventos";
            default:
                return "Cantidad";
        }
    };

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0];
            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium">{data.name}</p>
                    <p style={{ color: data.color }}>
                        {getTooltipLabel()}: {data.value}
                    </p>
                </div>
            );
        }
        return null;
    };

    return (
        <Card
            title={
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <BarChart3 size={20} className="text-blue-500" />
                        <span>Programaciones</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Radio.Group
                            value={chartMode}
                            onChange={(e) => setChartMode(e.target.value)}
                            size="small"
                            buttonStyle="solid"
                        >
                            <Radio.Button value="eventType">
                                Tipo de Evento
                            </Radio.Button>
                            <Radio.Button value="eventState">Estado</Radio.Button>
                            <Radio.Button value="enrollmentsByType">
                                Inscritos
                            </Radio.Button>
                        </Radio.Group>
                    </div>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-64">
                {chartData.length && !isLoading ? (
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            <Pie
                                data={chartData}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={renderCustomizedLabel}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                            >
                                {chartData.map((_, index) => (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={COLORS[index % COLORS.length]}
                                    />
                                ))}
                            </Pie>
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                        </PieChart>
                    </ResponsiveContainer>
                ) : (
                    <div className="h-full flex items-center justify-center">
                        <Empty description="No hay datos disponibles" />
                    </div>
                )}
            </div>
        </Card>
    );
};

export default EventPieChartWithToggle;
