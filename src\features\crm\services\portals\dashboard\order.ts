import { portalsApi } from "@services/portals";
import type {
    DashboardOrderQueryParams,
    DashboardOrderSummaryData,
    DashboardOrderAgentsData,
    DashboardOrderAnalyticsData,
    DashboardOrderHistoricalData,
    DashboardOrderProductsData,
    DashboardOrderRecentOrdersData,
} from "@/features/crm/types/dashboard/orders";

// Summary endpoint - Más importante, se carga primero
export const getOrderDashboardSummary = async (
    query: DashboardOrderQueryParams = {},
): Promise<DashboardOrderSummaryData> => {
    const response = await portalsApi.get("crm/dashboard/sales/summary", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Agents endpoint
export const getOrderDashboardAgents = async (
    query: DashboardOrderQueryParams = {},
): Promise<DashboardOrderAgentsData> => {
    const response = await portalsApi.get("crm/dashboard/sales/agents", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Analytics endpoint
export const getOrderDashboardAnalytics = async (
    query: DashboardOrderQueryParams = {},
): Promise<DashboardOrderAnalyticsData> => {
    const response = await portalsApi.get("crm/dashboard/sales/analytics", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Historical endpoint
export const getOrderDashboardHistorical = async (
    query: DashboardOrderQueryParams = {},
): Promise<DashboardOrderHistoricalData> => {
    const response = await portalsApi.get("crm/dashboard/sales/historical", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Products endpoint
export const getOrderDashboardProducts = async (
    query: DashboardOrderQueryParams = {},
): Promise<DashboardOrderProductsData> => {
    const response = await portalsApi.get("crm/dashboard/sales/products", {
        params: {
            ...query,
        },
    });
    return response.data;
};

// Recent orders endpoint
export const getOrderDashboardRecentOrders = async (
    query: DashboardOrderQueryParams = {},
): Promise<DashboardOrderRecentOrdersData> => {
    const response = await portalsApi.get("crm/dashboard/sales/recent-orders", {
        params: {
            ...query,
        },
    });
    return response.data;
};

export const invalidateOrderDashboardCache = () => {
    return portalsApi.post("crm/dashboard/sales/invalidate-cache");
};
