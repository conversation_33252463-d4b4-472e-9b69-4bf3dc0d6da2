import { Button, Form, Input, Select, DatePicker, Spin } from "antd";
import {
    ActivityStatus,
    ACTIVITY_STATUS_CHOICES,
    ActivityUpdate,
    ActivityRetrieve,
} from "../../types/activity";
import { useUpdateActivity, useDeleteActivity } from "../../hooks/use-activity";
import SelectOrder from "../molecules/select-order";
import SelectStaffUser from "../molecules/select-staff-user";
import {
    Save,
    Trash,
    ExternalLink,
    Mail,
    Phone,
    CheckCircle,
    Clock,
    Play,
} from "lucide-react";
import dayjs, { Dayjs } from "dayjs";
import { useEffect } from "react";
import { Link } from "react-router-dom";

const { TextArea } = Input;

interface EditActivityFormProps {
    activity: ActivityRetrieve;
    onFinish?: () => void;
    loading?: boolean;
}

interface ActivityFormData {
    title: string;
    description?: string;
    deadline?: Dayjs;
    responsible?: string;
    status: ActivityStatus;
    order?: string;
}

const EditActivityForm = ({ activity, onFinish, loading }: EditActivityFormProps) => {
    const [form] = Form.useForm<ActivityFormData>();
    const updateActivityMutation = useUpdateActivity({
        onUpdateActivitySuccess: () => {
            onFinish?.();
        },
    });
    const deleteActivityMutation = useDeleteActivity({
        onDeleteActivitySuccess: () => {
            onFinish?.();
        },
    });

    useEffect(() => {
        if (activity) {
            form.setFieldsValue({
                title: activity.title || "",
                description: activity.description || "",
                deadline: activity.deadline ? dayjs(activity.deadline) : undefined,
                responsible: activity.responsible?.uid || undefined,
                status: activity.status,
                order: activity.order?.oid || undefined,
            });
        }
    }, [activity, form]);

    const handleSubmit = (values: ActivityFormData) => {
        const activityData: ActivityUpdate = {
            title: values.title,
            description: values.description || null,
            deadline: values.deadline ? values.deadline.format("YYYY-MM-DD") : null,
            responsible: values.responsible || null,
            status: values.status,
            order: values.order || null,
        };

        updateActivityMutation.mutate({
            aid: activity.aid,
            activity: activityData,
        });
    };

    const handleDelete = () => {
        deleteActivityMutation.mutate(activity.aid);
    };

    const getStatusConfig = () => {
        switch (activity.status) {
            case ActivityStatus.COMPLETED:
                return {
                    color: "bg-green-100 text-green-800 border border-green-300",
                    icon: <CheckCircle className="w-4 h-4 mr-1.5" />,
                    text: "Completada",
                };
            case ActivityStatus.IN_PROGRESS:
                return {
                    color: "bg-blue-100 text-blue-800 border border-blue-300",
                    icon: <Play className="w-4 h-4 mr-1.5" />,
                    text: "En Progreso",
                };
            case ActivityStatus.PENDING:
                return {
                    color: "bg-amber-50 text-amber-800 border border-amber-200",
                    icon: <Clock className="w-4 h-4 mr-1.5" />,
                    text: "Pendiente",
                };
            default:
                return {
                    color: "bg-gray-100 text-gray-800 border border-gray-200",
                    icon: <Clock className="w-4 h-4 mr-1.5" />,
                    text: "Desconocido",
                };
        }
    };

    const statusConfig = getStatusConfig();

    if (loading) {
        return (
            <div className="flex justify-center items-center py-8">
                <Spin size="large" />
            </div>
        );
    }

    return (
        <Form
            name="edit-activity-form"
            layout="vertical"
            form={form}
            onFinish={handleSubmit}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="col-span-4 space-y-6">
                    {/* Order Information */}
                    {activity.order && (
                        <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                            <p className="text-gray-400 font-semibold text-sm uppercase">
                                SOBRE LA ORDEN
                            </p>
                            <div className="px-1 rounded-md hover:bg-gray-50 transition-colors">
                                <div className="flex items-center gap-4">
                                    {activity.order.owner}
                                    <Link
                                        to={`/crm/orders/${activity.order.oid}`}
                                        className="flex items-center text-blue-600 hover:text-blue-800"
                                    >
                                        <span className="text-sm font-medium mr-1">
                                            #{activity.order.oid.slice(-6)}
                                        </span>
                                        <ExternalLink size={14} />
                                    </Link>
                                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                        {activity.order.stage
                                            .replace("_", " ")
                                            .toUpperCase()}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Responsible User Information */}
                    {activity.responsible && (
                        <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                            <p className="text-gray-400 font-semibold text-sm uppercase">
                                RESPONSABLE
                            </p>
                            <div className="px-1 rounded-md hover:bg-gray-50 transition-colors">
                                <div className="flex items-center gap-4">
                                    <p className="text-sm font-medium">
                                        {activity.responsible.fullName}
                                    </p>
                                </div>
                                <div className="flex gap-2">
                                    {activity.responsible.email && (
                                        <p className="text-xs text-gray-500 flex items-center mt-1">
                                            <Mail
                                                size={14}
                                                className="h-3.5 w-3.5 mr-1"
                                            />
                                            {activity.responsible.email}
                                        </p>
                                    )}
                                    <p className="text-xs text-gray-500 flex items-center mt-1">
                                        <Phone size={14} className="h-3.5 w-3.5 mr-1" />
                                        {activity.responsible.phoneNumber}
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Activity Details */}
                    <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                        <div className="flex items-center justify-between">
                            <p className="text-gray-400 font-semibold text-sm">
                                DETALLES DE LA ACTIVIDAD
                            </p>
                            <span
                                className={`text-sm px-3 py-1 rounded-full flex items-center font-medium ${statusConfig.color}`}
                            >
                                {statusConfig.icon}
                                {statusConfig.text}
                            </span>
                        </div>

                        <div className="grid grid-cols-1 gap-y-4">
                            <Form.Item
                                name="title"
                                label="Título"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            "Por favor ingresa un título para la actividad",
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Ej. Llamada de seguimiento al cliente"
                                    size="large"
                                />
                            </Form.Item>

                            <Form.Item name="description" label="Descripción">
                                <TextArea
                                    placeholder="Descripción detallada de la actividad..."
                                    rows={3}
                                    size="large"
                                />
                            </Form.Item>

                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <Form.Item
                                    name="status"
                                    label="Estado"
                                    rules={[
                                        {
                                            required: true,
                                            message: "Por favor selecciona un estado",
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="Seleccionar estado"
                                        size="large"
                                        options={ACTIVITY_STATUS_CHOICES.map(
                                            (choice) => ({
                                                value: choice.value,
                                                label: choice.label,
                                            }),
                                        )}
                                    />
                                </Form.Item>

                                <Form.Item name="deadline" label="Fecha límite">
                                    <DatePicker
                                        placeholder="Seleccionar fecha límite"
                                        size="large"
                                        format="DD/MM/YYYY"
                                        className="w-full"
                                        disabledDate={(current) =>
                                            current && current < dayjs().startOf("day")
                                        }
                                    />
                                </Form.Item>
                            </div>

                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                <Form.Item name="responsible" label="Responsable">
                                    <SelectStaffUser
                                        placeholder="Asignar responsable"
                                        size="large"
                                    />
                                </Form.Item>

                                <Form.Item name="order" label="Orden relacionada">
                                    <SelectOrder
                                        placeholder="Seleccionar orden"
                                        size="large"
                                        selectedOrder={activity.order || undefined}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Actions Sidebar */}
                <div className="col-span-2 space-y-6">
                    <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                        <p className="text-gray-400 font-semibold text-sm uppercase">
                            ACCIONES
                        </p>
                        <div className="space-y-3">
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={updateActivityMutation.isPending}
                                icon={<Save size={16} />}
                                size="large"
                                className="w-full"
                            >
                                Guardar cambios
                            </Button>
                            <Button
                                danger
                                onClick={handleDelete}
                                loading={deleteActivityMutation.isPending}
                                icon={<Trash size={16} />}
                                size="large"
                                className="w-full"
                            >
                                Eliminar actividad
                            </Button>
                        </div>
                    </div>

                    {/* Activity Info */}
                    <div className="bg-white-full rounded-lg shadow-sm p-4 space-y-4">
                        <p className="text-gray-400 font-semibold text-sm uppercase">
                            INFORMACIÓN
                        </p>
                        <div className="space-y-3 text-sm">
                            <div>
                                <span className="text-gray-500">ID:</span>
                                <span className="ml-2 font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                                    {activity.aid}
                                </span>
                            </div>
                            <div>
                                <span className="text-gray-500">Creado:</span>
                                <span className="ml-2">
                                    {dayjs(activity.createdAt).format(
                                        "DD/MM/YYYY HH:mm",
                                    )}
                                </span>
                            </div>
                            <div>
                                <span className="text-gray-500">Actualizado:</span>
                                <span className="ml-2">
                                    {dayjs(activity.updatedAt).format(
                                        "DD/MM/YYYY HH:mm",
                                    )}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
};

export default EditActivityForm;
