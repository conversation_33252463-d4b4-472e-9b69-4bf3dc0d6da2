import React from "react";
import { Card, Table, Progress, Empty } from "antd";
import { Handshake } from "lucide-react";
import type { TopAlliance } from "@/features/crm/types/dashboard/events";
import type { ColumnsType } from "antd/es/table";

interface TopAlliancesTableProps {
    data?: TopAlliance[];
    isLoading?: boolean;
}

const TopAlliancesTable: React.FC<TopAlliancesTableProps> = ({
    data = [],
    isLoading = false,
}) => {
    const columns: ColumnsType<TopAlliance> = [
        {
            title: "Alianza",
            dataIndex: "allianceName",
            key: "allianceName",
            width: "25%",
            render: (text: string) => (
                <span className="font-medium text-gray-800">{text}</span>
            ),
        },
        {
            title: "Cantidad de Eventos",
            dataIndex: "associatedEventsCount",
            key: "associatedEventsCount",
            width: "15%",
            align: "center",
            render: (count: number) => (
                <span className="text-blue-600 font-semibold">{count}</span>
            ),
        },
        {
            title: "Contactos Generados",
            dataIndex: "uniqueEnrollments",
            key: "uniqueEnrollments",
            width: "15%",
            align: "center",
            render: (count: number) => (
                <span className="text-green-600 font-semibold">{count}</span>
            ),
        },
        {
            title: "Total de Inscritos",
            dataIndex: "totalEnrollments",
            key: "totalEnrollments",
            width: "15%",
            align: "center",
            render: (count: number) => (
                <span className="text-purple-600 font-semibold">{count}</span>
            ),
        },
        // vamos a unir a la columna de porcentaje en una sola
        {
            title: "Participación",
            dataIndex: ["globalParticipationPercentage", "participationPercentage"],
            key: "participationPercentage",
            width: "15%",
            align: "start",
            render: (_, record: TopAlliance) => (
                <div className="flex flex-col">
                    <div>
                        <span>Vs Otras Alianzas</span>
                        <Progress
                            percent={record.participationPercentage}
                            size="small"
                            strokeColor="#52c41a"
                            showInfo={true}
                            format={(percent) => `${percent}%`}
                        />
                    </div>
                    <div>
                        <span>Vs Total de Inscritos</span>
                        <Progress
                            percent={record.globalParticipationPercentage}
                            size="small"
                            strokeColor="#1890ff"
                            showInfo={true}
                            format={(percent) => `${percent}%`}
                        />
                    </div>
                </div>
            ),
        },
    ];

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Handshake size={20} className="text-blue-500" />
                    <span>Top Alianzas</span>
                </div>
            }
            className="shadow-md"
        >
            {data.length && !isLoading ? (
                <Table
                    columns={columns}
                    dataSource={data}
                    rowKey="alliance_name"
                    pagination={false}
                    size="small"
                    scroll={{ x: 800 }}
                    className="w-full"
                />
            ) : (
                <div className="py-8">
                    <Empty description="No hay datos de alianzas disponibles" />
                </div>
            )}
        </Card>
    );
};

export default TopAlliancesTable;
