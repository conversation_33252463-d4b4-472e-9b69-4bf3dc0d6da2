import { useQuery } from "@tanstack/react-query";
import { getLeadSources } from "../services/portals/lead-sources";

export const useLeadSources = () => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["lead-sources"],
        queryFn: () => getLeadSources(),
    });

    const { count: COUNT, results: leadSources } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        leadSources,
        COUNT,
    };
};
