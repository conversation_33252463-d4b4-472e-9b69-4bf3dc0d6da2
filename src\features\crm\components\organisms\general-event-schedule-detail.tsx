import {
    EventModality,
    EventModalityLabels,
    EventStage,
    EventStageLabels,
} from "@/features/crm/types/event";
import {
    CreateEventScheduleFormBody,
    RetrieveEventSchedule,
    UpdateEventScheduleFormValues,
} from "@/features/crm/types/event-schedule";
import {
    Button,
    DatePicker,
    Form,
    Input,
    Select,
    Radio,
    message,
    Collapse,
} from "antd";
import { Save, Trash, Settings } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectInstructor from "@/features/crm/components/molecules/select-instructor";
import { updateEventSchedule } from "../../services/portals/event-schedule";
import dayjs from "dayjs";
import SelectPartnership from "../molecules/select-partnership";
import PartnershipEnrollmentUrlsList from "../molecules/partnership-enrollment-urls-list";
import GeneralEnrollmentUrl from "../molecules/general-enrollment-url";
import ExternalEventLink from "../molecules/external-event-link";
import UploadEventScheduleThumbnail from "../molecules/upload-event-schedule-thumbnail";
import UploadEventScheduleCover from "../molecules/upload-event-schedule-cover";
import InvitationForm from "../molecules/edit-event-schedule-invitation-form";
import { useApiError } from "@hooks/use-api-error";
import type { AxiosError } from "axios";

const { TextArea } = Input;

type GeneralEventDetailProps = {
    eventSchedule: RetrieveEventSchedule;
};

export default function GeneralEventDetail({ eventSchedule }: GeneralEventDetailProps) {
    const [form] = Form.useForm<UpdateEventScheduleFormValues>();

    const queryClient = useQueryClient();

    const { handleError } = useApiError();
    const { mutate: onEventUpdate, isPending: isEventUpdateLoading } = useMutation({
        mutationFn: (partialEvent: Partial<CreateEventScheduleFormBody>) =>
            updateEventSchedule(eventSchedule.esid, partialEvent),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["event-schedule", eventSchedule.esid],
            });
            message.success("El evento ha sido actualizado correctamente");
        },
        onError: (error: AxiosError) => {
            handleError(error);
        },
    });

    const handleFormFinish = (values: UpdateEventScheduleFormValues) => {
        // Validar el rango de WhatsApp antes de enviar
        if (values.isWhatsappActive && values.whatsappDelayRange) {
            const [min, max] = values.whatsappDelayRange;

            if (min >= max) {
                message.error("El valor mínimo debe ser menor al máximo");
                return;
            }

            if (min < 0 || max < 0) {
                message.error("Los valores deben ser positivos");
                return;
            }
        }

        // Preparar datos de actualización incluyendo campos de invitación
        const updateData = {
            name: values.name,
            modality: values.modality,
            stage: values.stage,
            partnerships: values.partnerships,
            description: values.description,
            location: values.location,
            instructor: values.instructor,
            price: values.price,
            isGeneral: values.isGeneral,
            startDate: values.rangeDate[0].toISOString(),
            endDate: values.rangeDate[1].toISOString(),
            // Campos de invitación
            whatsappTemplate: values.whatsappTemplate,
            isWhatsappActive: values.isWhatsappActive,
            ...(values.isWhatsappActive
                ? {
                      scheduledDatetimeWhatsapp:
                          values.scheduledDatetimeWhatsapp?.toISOString(),
                      whatsappDelayRange: values.whatsappDelayRange,
                  }
                : {
                      scheduledDatetimeWhatsapp: null,
                      whatsappDelayRange: null,
                  }),
            emailsReminderAuto: values.emailsReminderAuto,
            ...(!values.emailsReminderAuto
                ? {
                      scheduledDatetimeEmail:
                          values.scheduledDatetimeEmail?.toISOString(),
                  }
                : {
                      scheduledDatetimeEmail: null,
                  }),
        };

        onEventUpdate(updateData);
    };

    const modalitySelectOptions = Object.values(EventModality).map((value) => ({
        value,
        label: EventModalityLabels[value],
    }));

    const stageSelectOptions = Object.values(EventStage).map((value) => ({
        value,
        label: EventStageLabels[value],
    }));

    return (
        <Form
            name="generalEventForm"
            layout="vertical"
            form={form}
            initialValues={{
                name: eventSchedule.name,
                description: eventSchedule.description,
                modality: eventSchedule.modality,
                stage: eventSchedule.stage,
                instructor: eventSchedule.instructor?.iid,
                location: eventSchedule.location,
                price: eventSchedule.price,
                partnerships: eventSchedule.partnerships.map((p) => p.pid),
                isGeneral: eventSchedule.isGeneral,
                rangeDate: [
                    dayjs(eventSchedule.startDate),
                    dayjs(eventSchedule.endDate),
                ],
                // Campos de invitación
                whatsappTemplate: eventSchedule.whatsappTemplate?.tid,
                scheduledDatetimeEmail: eventSchedule.scheduledDatetimeEmail
                    ? dayjs(eventSchedule.scheduledDatetimeEmail)
                    : undefined,
                scheduledDatetimeWhatsapp: eventSchedule.scheduledDatetimeWhatsapp
                    ? dayjs(eventSchedule.scheduledDatetimeWhatsapp)
                    : undefined,
                whatsappDelayRange:
                    eventSchedule.whatsappDelayRange?.length === 2
                        ? [
                              eventSchedule.whatsappDelayRange[0],
                              eventSchedule.whatsappDelayRange[1],
                          ]
                        : [0, 0],
                isWhatsappActive: eventSchedule.isWhatsappActive || false,
                emailsReminderAuto: eventSchedule.emailsReminderAuto === true || false,
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm h-fit">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-x-4">
                        <Form.Item<UpdateEventScheduleFormValues>
                            name="name"
                            label={<FormLabel>Nombre del evento</FormLabel>}
                            className="col-span-2"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el nombre del evento.",
                                },
                            ]}
                        >
                            <Input placeholder="Ej. ¿Cómo ingresar a los CEUs?" />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="modality"
                            label={<FormLabel>Modalidad</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la modalidad.",
                                },
                            ]}
                        >
                            <Select options={modalitySelectOptions} />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="stage"
                            label={<FormLabel>Etapa</FormLabel>}
                            className="col-span-2"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la etapa.",
                                },
                            ]}
                        >
                            <Radio.Group
                                optionType="button"
                                buttonStyle="solid"
                                options={stageSelectOptions}
                                className="w-full"
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="isGeneral"
                            label={<FormLabel>Tipo de evento</FormLabel>}
                            tooltip="Si el horario se marca como general aparecerá en la website"
                        >
                            <Radio.Group
                                optionType="button"
                                buttonStyle="solid"
                                options={[
                                    { value: false, label: "Específico" },
                                    { value: true, label: "General" },
                                ]}
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="partnerships"
                            label={<FormLabel>Alianzas</FormLabel>}
                            className="col-span-3"
                        >
                            <SelectPartnership />
                        </Form.Item>

                        <PartnershipEnrollmentUrlsList
                            partnershipEnrollmentUrls={
                                eventSchedule.partnershipEnrollmentUrls
                            }
                            className="col-span-3 mb-4"
                        />

                        <GeneralEnrollmentUrl
                            enrollmentUrl={eventSchedule.enrollmentUrl}
                            isGeneral={eventSchedule.isGeneral}
                            className="col-span-3 mb-4"
                        />

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="description"
                            label={<FormLabel>Descripción</FormLabel>}
                            className="col-span-3"
                        >
                            <TextArea
                                rows={4}
                                placeholder="Descripción del evento..."
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="location"
                            label={<FormLabel>Ubicación</FormLabel>}
                        >
                            <Input placeholder="Ej. Zoom, Google Meet, etc." />
                        </Form.Item>
                        <Form.Item
                            name="instructor"
                            label={<FormLabel>Instructor</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione un instructor.",
                                },
                            ]}
                        >
                            <SelectInstructor />
                        </Form.Item>
                    </div>
                </div>

                <div className="col-span-2 space-y-2">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                block
                            >
                                Eliminar
                            </Button>
                            <Button
                                block
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isEventUpdateLoading}
                                onClick={() => form.submit()}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">FECHAS</p>
                        <div>
                            <Form.Item<UpdateEventScheduleFormValues>
                                name="rangeDate"
                                label={<FormLabel>Fecha de Inicio/Fin</FormLabel>}
                            >
                                <DatePicker.RangePicker
                                    showTime
                                    className="w-full"
                                    format="DD/MM/YYYY HH:mm"
                                />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="bg-white-full rounded-lg shadow-sm">
                        <Collapse
                            // open if isWhatsappActive is true or if emailsReminderAuto is true
                            defaultActiveKey={
                                eventSchedule.isWhatsappActive ||
                                !eventSchedule.emailsReminderAuto
                                    ? ["1"]
                                    : []
                            }
                            ghost
                            size="small"
                            expandIcon={({ isActive }) => (
                                <Settings
                                    size={14}
                                    className={`text-gray-400 transition-transform ${isActive ? "rotate-90" : ""}`}
                                />
                            )}
                            items={[
                                {
                                    key: "1",
                                    label: (
                                        <div className="flex items-center gap-2">
                                            <p className="text-gray-400 font-semibold text-sm">
                                                CONFIGURAR INVITACIONES
                                            </p>
                                        </div>
                                    ),
                                    children: (
                                        <InvitationForm
                                            eventSchedule={eventSchedule}
                                            form={form}
                                            className="mt-0 pt-0"
                                        />
                                    ),
                                },
                            ]}
                            className="border-none"
                        />
                    </div>

                    <ExternalEventLink extEventLink={eventSchedule.extEventLink} />

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">
                            CONTENIDO MULTIMEDIA
                        </p>
                        <div>
                            <FormLabel>Miniatura</FormLabel>
                            <UploadEventScheduleThumbnail
                                esid={eventSchedule.esid}
                                initialEventScheduleThumbnail={eventSchedule.thumbnail}
                            />
                        </div>

                        <div>
                            <FormLabel>Portada</FormLabel>
                            <UploadEventScheduleCover
                                esid={eventSchedule.esid}
                                initialEventScheduleCoverImage={
                                    eventSchedule.coverImage
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
}
