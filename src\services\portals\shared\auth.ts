import { portalsApi } from "..";

export const login = async (credentials: { username: string; password: string }) => {
    const res = await portalsApi.post("/erp/auth/login", credentials);
    return res.data;
};

export const checkToken = async () => {
    const res = await portalsApi.post("/erp/auth/check-token");
    return res.data;
};

export const logout = async () => {
    const res = await portalsApi.post("/erp/auth/logout");
    return res.data;
};