import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import { Term } from "../../types/contact";

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 100;

export const getTerms = async (): Promise<PaginatedResponse<Term>> => {
    const response = await portalsApi.get("crm/terms", {
        params: {
            page: DEFAULT_PAGE,
            pageSize: DEFAULT_PAGE_SIZE,
        },
    });
    return response.data;
};

export const createTerm = async (name: string): Promise<Term> => {
    const response = await portalsApi.post("crm/terms", { name });
    return response.data;
};
