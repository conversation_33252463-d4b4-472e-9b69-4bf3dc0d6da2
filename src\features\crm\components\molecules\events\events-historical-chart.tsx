import React from "react";
import { Card, Empty } from "antd";
import {
    <PERSON><PERSON>hart,
    Bar,
    XAxis,
    <PERSON>Axis,
    CartesianGrid,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
} from "recharts";
import { Calendar } from "lucide-react";
import dayjs from "dayjs";
import type { DashboardEventsHistoricalData } from "@/features/crm/types/dashboard/events";

interface EventsHistoricalChartProps {
    data?: DashboardEventsHistoricalData;
    isLoading?: boolean;
}

interface ChartDataItem {
    eventName: string;
    formattedDate: string;
    totalEnrollments: number;
    // Columna 1: Inscritos con/sin universidad
    withUniversity: number;
    withoutUniversity: number;
    // Columna 2: Top universidades (máximo 5)
    university1?: number;
    university2?: number;
    university3?: number;
    university4?: number;
    university5?: number;
    // Metadata para tooltips
    university1Name?: string;
    university2Name?: string;
    university3Name?: string;
    university4Name?: string;
    university5Name?: string;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: Array<{
        value: number;
        dataKey: string;
        name: string;
        color: string;
        payload: ChartDataItem;
    }>;
    label?: string;
}

const EventsHistoricalChart: React.FC<EventsHistoricalChartProps> = ({
    data,
    isLoading,
}) => {
    // Transformar datos para el gráfico
    const chartData: ChartDataItem[] = React.useMemo(() => {
        if (!data?.data) return [];

        return data.data.map((event) => {
            // Calcular inscritos con universidad (suma de top universidades)
            const withUniversity = event.topEducationalInstitutions.reduce(
                (sum, inst) => sum + inst.enrollments,
                0,
            );

            // Calcular inscritos sin universidad
            const withoutUniversity = event.totalEnrollments - withUniversity;

            // Formatear fecha como "08/07 - 8pm"
            const startDate = dayjs(event.startDate);
            const formattedDate = `${startDate.format("DD/MM")} - ${startDate.format("ha")}`;

            // Preparar datos de universidades (máximo 5)
            const universities = event.topEducationalInstitutions.slice(0, 5);
            const chartItem: ChartDataItem = {
                eventName: event.eventName,
                formattedDate,
                totalEnrollments: event.totalEnrollments,
                withUniversity,
                withoutUniversity,
            };

            // Agregar universidades dinámicamente
            universities.forEach((uni, index) => {
                const key = `university${index + 1}` as keyof ChartDataItem;
                const nameKey = `university${index + 1}Name` as keyof ChartDataItem;
                (chartItem as any)[key] = uni.enrollments;
                (chartItem as any)[nameKey] = uni.name;
            });

            return chartItem;
        });
    }, [data]);

    // Tooltip personalizado para columna de inscritos (con/sin universidad)
    const EnrollmentTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            const withUniversityPercentage =
                data.totalEnrollments > 0
                    ? ((data.withUniversity / data.totalEnrollments) * 100).toFixed(1)
                    : "0";
            const withoutUniversityPercentage =
                data.totalEnrollments > 0
                    ? ((data.withoutUniversity / data.totalEnrollments) * 100).toFixed(
                          1,
                      )
                    : "0";

            return (
                <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                    <p className="font-medium mb-2">{data.eventName}</p>
                    <p className="text-sm mb-1">
                        <strong>Total de inscritos:</strong> {data.totalEnrollments}
                    </p>
                    <div className="space-y-1">
                        <p className="text-sm" style={{ color: "#4096ff" }}>
                            Con universidad: {data.withUniversity} (
                            {withUniversityPercentage}%)
                        </p>
                        <p className="text-sm" style={{ color: "#36cfc9" }}>
                            Sin universidad: {data.withoutUniversity} (
                            {withoutUniversityPercentage}%)
                        </p>
                    </div>
                </div>
            );
        }
        return null;
    };

    // Tooltip personalizado para columna de universidades
    const UniversityTooltip = ({ active, payload, label }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            const hoveredEntry = payload.find((entry) => entry.value > 0);

            if (hoveredEntry) {
                const universityKey = hoveredEntry.dataKey;
                const nameKey = `${universityKey}Name` as keyof ChartDataItem;
                const universityName = data[nameKey] as string;
                const enrollments = hoveredEntry.value;
                const percentage =
                    data.totalEnrollments > 0
                        ? ((enrollments / data.totalEnrollments) * 100).toFixed(1)
                        : "0";

                return (
                    <div className="bg-white-full p-3 border border-gray-200 shadow-md rounded">
                        <p className="font-medium mb-2">{data.eventName}</p>
                        <p className="text-sm mb-1">
                            <strong>Universidad:</strong> {universityName}
                        </p>
                        <p className="text-sm" style={{ color: hoveredEntry.color }}>
                            Inscritos: {enrollments} ({percentage}% del total)
                        </p>
                    </div>
                );
            }
        }
        return null;
    };

    // Colores para las universidades
    const universityColors = ["#faad14", "#73d13d", "#ff4d4f", "#722ed1", "#eb2f96"];

    if (isLoading) {
        return (
            <Card
                title={
                    <div className="flex items-center gap-2">
                        <Calendar size={20} className="text-blue-500" />
                        <span>Histórico de Eventos</span>
                    </div>
                }
                className="shadow-md h-full"
            >
                <div className="h-64 flex items-center justify-center">
                    <div className="animate-pulse">Cargando...</div>
                </div>
            </Card>
        );
    }

    if (!chartData.length) {
        return (
            <Card
                title={
                    <div className="flex items-center gap-2">
                        <Calendar size={20} className="text-blue-500" />
                        <span>Histórico de Eventos</span>
                    </div>
                }
                className="shadow-md h-full"
            >
                <Empty description="No hay datos disponibles" />
            </Card>
        );
    }

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Calendar size={20} className="text-blue-500" />
                    <span>Histórico de Eventos</span>
                </div>
            }
            className="shadow-md h-full"
        >
            <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={chartData}
                        margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 60,
                        }}
                    >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey="formattedDate"
                            angle={-45}
                            textAnchor="end"
                            height={80}
                            interval={0}
                            tick={{ fontSize: 12 }}
                        />
                        <YAxis tick={{ fontSize: 12 }} />

                        {/* Columna 1: Inscritos con/sin universidad */}
                        <Bar
                            dataKey="withUniversity"
                            stackId="enrollment"
                            fill="#4096ff"
                            name="Con Universidad"
                            radius={[0, 0, 0, 0]}
                        />
                        <Bar
                            dataKey="withoutUniversity"
                            stackId="enrollment"
                            fill="#36cfc9"
                            name="Sin Universidad"
                            radius={[4, 4, 0, 0]}
                        />

                        {/* Columna 2: Top universidades */}
                        {[1, 2, 3, 4, 5].map((index) => (
                            <Bar
                                key={`university${index}`}
                                dataKey={`university${index}`}
                                stackId="universities"
                                fill={universityColors[index - 1]}
                                name={`Universidad ${index}`}
                                radius={index === 5 ? [4, 4, 0, 0] : [0, 0, 0, 0]}
                            />
                        ))}

                        {/* Tooltips independientes */}
                        <Tooltip
                            content={<EnrollmentTooltip />}
                            cursor={{ fill: "rgba(64, 150, 255, 0.1)" }}
                        />

                        <Legend />
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

export default EventsHistoricalChart;
