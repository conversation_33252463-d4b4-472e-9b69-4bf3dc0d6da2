type PreferencesState = {
    currentApp: string;
};

const DEFAULT_PREFERENCES: PreferencesState = {
    currentApp: "erp",
};

export class PreferencesService {
    private preferences: PreferencesState;

    constructor() {
        this.preferences = this.loadPreferences();
    }

    private loadPreferences(): PreferencesState {
        console.log("Loading preferences state");
        try {
            const storedData = localStorage.getItem("preferencesData");
            if (storedData) {
                return JSON.parse(storedData) as PreferencesState;
            }
        } catch (error) {
            console.error("Error loading preferences state:", error);
        }
        return DEFAULT_PREFERENCES;
    }

    getCurrentApp(): string {
        return this.preferences.currentApp;
    }

    setCurrentApp(currentApp: string): void {
        this.preferences = {
            currentApp,
        };
        this.savePreferences();
    }

    private savePreferences(): void {
        localStorage.setItem("preferencesData", JSON.stringify(this.preferences));
    }
}

export const preferencesService = new PreferencesService();
