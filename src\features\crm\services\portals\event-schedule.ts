import { portalsApi } from "@services/portals";
import {
    EventSchedule,
    CreateEventScheduleFormBody,
    ListEventSchedulesQueryParams,
    RetrieveEventSchedule,
} from "../../types/event-schedule";
import { UploadFile } from "antd";

export const listEventSchedules = async (
    queryParams: ListEventSchedulesQueryParams = {},
) => {
    const res = await portalsApi.get("crm/event-schedules", { params: queryParams });
    return res.data;
};

export const createEventSchedule = async (
    newEventSchedule: CreateEventScheduleFormBody,
): Promise<EventSchedule> => {
    const res = await portalsApi.post("crm/event-schedules", newEventSchedule);
    return res.data;
};

export const getEventSchedule = async (id: string): Promise<RetrieveEventSchedule> => {
    const { data } = await portalsApi.get(`crm/event-schedules/${id}`);
    return data;
};

export const updateEventSchedule = async (
    esid: string,
    data: Partial<CreateEventScheduleFormBody>,
): Promise<EventSchedule> => {
    const response = await portalsApi.patch(`crm/event-schedules/${esid}`, data);
    return response.data;
};

export const uploadEventScheduleCoverImage = async (eid: string, file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post(
        `crm/event-schedules/${eid}/upload-cover-image`,
        formData,
    );
    return response.data;
};
export const removeEventScheduleCoverImage = async (eid: string, fid: string) => {
    const response = await portalsApi.delete(
        `crm/event-schedules/${eid}/remove-cover-image/${fid}`,
    );
    return response.data;
};

export const uploadEventScheduleThumbnail = async (eid: string, file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post(
        `crm/event-schedules/${eid}/upload-thumbnail`,
        formData,
    );
    return response.data;
};

export const removeEventScheduleThumbnail = async (eid: string, fid: string) => {
    const response = await portalsApi.delete(
        `crm/event-schedules/${eid}/remove-thumbnail/${fid}`,
    );
    return response.data;
};
