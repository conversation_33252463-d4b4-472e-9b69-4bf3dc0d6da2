import React from "react";
import { Card, Table, Tag, Tooltip } from "antd";
import { Package, ArrowUpRight } from "lucide-react";
import clsx from "clsx";
import { TopSellingProduct } from "../../types/dashboard/orders";

interface TopProductsCardProps {
    title: string;
    data: TopSellingProduct[];
    icon?: React.ReactNode;
    maxRevenue?: number; // Used to calculate the proportion for the progress bar
    className?: string;
}

export default function TopProductsCard({
    title,
    data,
    icon = <Package className="h-5 w-5 text-blue-500" />,
    className,
}: TopProductsCardProps) {
    const TendencyCell = (_: string, record: TopSellingProduct) => {
        const { participation } = record;
        const { tendency, change } = participation;

        const getTendency = () => {
            let tendencyPrefix = "";
            let tendencyColor = "text-gray-500";
            let Icon = null;

            if (tendency === "up") tendencyPrefix = "+";
            else tendencyPrefix = "";

            if (tendency === "up") tendencyColor = "text-green-500";
            else if (tendency === "down") tendencyColor = "text-red-500";

            if (tendency === "up") Icon = <ArrowUpRight size={16} />;
            else if (tendency === "down")
                Icon = <ArrowUpRight size={16} className="rotate-90" />;

            return {
                tendencyPrefix,
                tendencyColor,
                Icon,
            };
        };

        return (
            <div className={`flex items-center ${getTendency().tendencyColor}`}>
                {getTendency().Icon}
                <span className="ml-1">
                    {getTendency().tendencyPrefix}
                    {change}%
                </span>
            </div>
        );
    };

    const columns = [
        {
            title: "Producto",
            dataIndex: "name",
            key: "name",
            render: (name: string) => <div className="font-medium">{name}</div>,
        },
        {
            title: "Ventas",
            dataIndex: "count",
            key: "count",
            render: (count: number) => <Tag color="blue">{count} unidades</Tag>,
        },
        {
            title: (
                <Tooltip title="Ingresos estimados totales generados por este producto.">
                    Ingresos
                </Tooltip>
            ),
            dataIndex: "revenue",
            key: "revenue",
            render: (revenue: number) => (
                <div className="flex items-center">
                    <span className="font-semibold text-xs">
                        S/. {revenue?.toLocaleString() ?? "0.00"}
                    </span>
                </div>
            ),
        },
        {
            title: (
                <Tooltip title="Dinero efectivamente recaudado (proporcional al total del ingreso por orden).">
                    Recaudación
                </Tooltip>
            ),
            dataIndex: "effectiveRevenue",
            key: "effectiveRevenue",
            render: (effectiveRevenue: number) => (
                <span className="font-semibold text-xs text-green-700">
                    S/. {effectiveRevenue?.toLocaleString() ?? "0.00"}
                </span>
            ),
        },
        {
            title: (
                <Tooltip title="Diferencia entre ingreso estimado e ingreso efectivo.">
                    Deuda
                </Tooltip>
            ),
            dataIndex: "debt",
            key: "debt",
            render: (debt: number) => (
                <span className="font-semibold text-xs text-yellow-500">
                    S/. {debt?.toLocaleString() ?? "0.00"}
                </span>
            ),
        },
        {
            title: (
                <Tooltip title="Pagos que se han perdido (reembolos y cancelaciones proporcionales al total del ingreso por orden).">
                    Pérdida
                </Tooltip>
            ),
            dataIndex: "lostRevenue",
            key: "lostRevenue",
            render: (lostRevenue: number) => (
                <span className="font-semibold text-xs text-red-600">
                    S/. {lostRevenue?.toLocaleString() ?? "0.00"}
                </span>
            ),
        },

        {
            title: (
                <Tooltip title="Cambio en la participación del producto respecto al total de ingresos comparado con el período anterior.">
                    Tendencia
                </Tooltip>
            ),
            key: "trend",
            render: (_: string, record: TopSellingProduct) => TendencyCell(_, record),
        },
    ];

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    {icon}
                    <span>{title}</span>
                </div>
            }
            className={clsx("shadow-md h-full", className)}
        >
            <Table
                dataSource={data.map((item, index) => ({ ...item, key: index }))}
                columns={columns}
                pagination={false}
                size="small"
            />
        </Card>
    );
}
