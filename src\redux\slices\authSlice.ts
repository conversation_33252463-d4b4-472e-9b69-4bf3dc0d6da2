import { createSlice, PayloadAction } from "@reduxjs/toolkit";

type AuthState = {
    token?: string;
    user?: {
        id?: number;
        username?: string;
        firstName?: string;
        lastName?: string;
        email?: string;
    };
    isAuthenticated: boolean;
};

const initialState: AuthState = {
    token: undefined,
    user: {
        id: undefined,
        username: undefined,
        firstName: undefined,
        lastName: undefined,
        email: undefined,
    },
    isAuthenticated: false,
};

const authSlice = createSlice({
    name: "auth",
    initialState,
    reducers: {
        login(state, action: PayloadAction<AuthState>) {
            state.token = action.payload.token;
            state.user = {
                id: action.payload.user?.id,
                username: action.payload.user?.username,
                firstName: action.payload.user?.firstName,
                lastName: action.payload.user?.lastName,
                email: action.payload.user?.email,
            };
            state.isAuthenticated = action.payload.isAuthenticated;
        },
        logout(state) {
            state.token = undefined;
            state.user = {
                id: undefined,
                username: undefined,
                firstName: undefined,
                lastName: undefined,
                email: undefined,
            };
            state.isAuthenticated = false;
        },
        refreshToken(state, action: PayloadAction<string>) {
            state.token = action.payload;
        },
    },
});

export type { AuthState };
export const { login, logout } = authSlice.actions;
export default authSlice.reducer;
