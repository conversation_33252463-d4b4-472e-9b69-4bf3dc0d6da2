import { App, <PERSON><PERSON>, Di<PERSON>r, Form, Input, Modal, Select, SelectProps } from "antd";
import { Plus } from "lucide-react";
import { useState } from "react";
import { useLeadSources } from "../../hooks/use-lead-sources";
import { useMutation } from "@tanstack/react-query";
import { LeadSourceCreate } from "../../types/lead-source";
import { createLeadSource } from "../../services/portals/lead-sources";
import FormLabel from "../atoms/FormLabel";
import queryClient from "@lib/queryClient";

const { TextArea } = Input;

type LeadSourceCreateForm = {
    name: string;
    description?: string;
};

interface SelectOrderLeadSourcesProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
}
export default function SelectOrderLeadSources({
    value,
    onChange,
    ...restProps
}: SelectOrderLeadSourcesProps) {
    const [modalOpen, setModalOpen] = useState(false);
    const { leadSources } = useLeadSources();
    const options = leadSources.map((leadSource) => ({
        value: leadSource.lsid,
        label: leadSource.name,
    }));

    const [form] = Form.useForm();
    const { notification, message } = App.useApp();

    const { mutate: createLeadSourceMutate, isPending } = useMutation({
        mutationFn: async (values: LeadSourceCreate) => createLeadSource(values),
        onSuccess: () => {
            message.success({
                content: "Fuente de lead creada exitosamente",
            });
            queryClient.invalidateQueries({
                queryKey: ["lead-sources"],
            });
            setModalOpen(false);
            form.resetFields();
        },
        onError: () => {
            notification.error({
                message: "Error al crear la fuente de lead",
                description: "Por favor, intenta nuevamente",
            });
        },
    });

    const handleCreateLeadSourceFinish = (values: LeadSourceCreateForm) => {
        const { name, description } = values;
        createLeadSourceMutate({
            name,
            description,
        });
    };

    return (
        <>
            <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        Crear Fuente de Lead
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <Form
                    name="create-lead-source-form"
                    form={form}
                    layout="vertical"
                    onFinish={handleCreateLeadSourceFinish}
                    initialValues={{
                        name: "",
                    }}
                >
                    <Form.Item<LeadSourceCreateForm>
                        name="name"
                        label={<FormLabel>Nombre</FormLabel>}
                        rules={[
                            {
                                required: true,
                                message:
                                    "Por favor, ingresa el nombre de la fuente de lead",
                            },
                        ]}
                    >
                        <Input placeholder="Ej. Facebook Ads" />
                    </Form.Item>
                    <Form.Item<LeadSourceCreateForm>
                        name="description"
                        label={<FormLabel>Descripción</FormLabel>}
                    >
                        <TextArea placeholder="Ej. Campaña de Facebook Ads para el programa de inglés" />
                    </Form.Item>
                    <div className="flex justify-end">
                        <Button
                            className="mr-2"
                            onClick={() => {
                                setModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isPending}>
                            Crear
                        </Button>
                    </div>
                </Form>
            </Modal>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={options}
                mode="multiple"
                allowClear
                placeholder="Selecciona los beneficios"
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras la fuente del lead que buscas?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
        </>
    );
}
