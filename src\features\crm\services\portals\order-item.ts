import type { CreateOrderItem, OrderItem } from "../../types/order";
import { portalsApi } from "@services/portals";

export const createOrderItem = async (
    orderItem: CreateOrderItem,
): Promise<OrderItem> => {
    const response = await portalsApi.post(`crm/order-items`, orderItem);
    return response.data;
};

export const partialUpdateOrderItem = async (
    orderItem: Partial<OrderItem>,
): Promise<Partial<OrderItem>> => {
    const response = await portalsApi.patch(
        `crm/order-items/${orderItem.oiid}`,
        orderItem,
    );
    return response.data;
};

export const deleteOrderItem = async (orderItem: OrderItem): Promise<void> => {
    await portalsApi.delete(`crm/order-items/${orderItem.oiid}`);
};

export const sendClassroomInvitation = async (orderItemId: string): Promise<void> => {
    await portalsApi.post(`crm/order-items/${orderItemId}/send-classroom-invitation`);
};
