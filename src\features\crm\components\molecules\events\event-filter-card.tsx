import React, { useState, useCallback } from "react";
import { Card, DatePicker, Select, Button, Row, Col } from "antd";
import { Filter, RotateCcw } from "lucide-react";
import { useSearchParams } from "react-router-dom";
import dayjs, { Dayjs } from "dayjs";
import "dayjs/locale/es";

dayjs.locale("es");

const { RangePicker } = DatePicker;

interface EventFilterCardProps {
    className?: string;
}

const EventFilterCard: React.FC<EventFilterCardProps> = ({ className }) => {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(() => {
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        if (startDate && endDate) {
            return [dayjs(startDate), dayjs(endDate)];
        }
        return null;
    });

    const [stage, setStage] = useState<string | undefined>(
        searchParams.get("stage") || undefined,
    );

    const [eventType, setEventType] = useState<string | undefined>(
        searchParams.get("eventType") || undefined,
    );

    const [modality, setModality] = useState<string | undefined>(
        searchParams.get("modality") || undefined,
    );

    const [partnerships, setPartnerships] = useState<string | undefined>(
        searchParams.get("partnerships") || undefined,
    );

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("stage");
            prev.delete("eventType");
            prev.delete("modality");
            prev.delete("partnerships");

            // Apply date range filters
            if (dateRange && dateRange[0] && dateRange[1]) {
                prev.set("startDate", dateRange[0].format("YYYY-MM-DD"));
                prev.set("endDate", dateRange[1].format("YYYY-MM-DD"));
            }

            // Apply other filters
            if (stage) prev.set("stage", stage);
            if (eventType) prev.set("eventType", eventType);
            if (modality) prev.set("modality", modality);
            if (partnerships) prev.set("partnerships", partnerships);

            return prev;
        });
    }, [dateRange, stage, eventType, modality, partnerships, setSearchParams]);

    const handleClearFilters = useCallback(() => {
        setDateRange(null);
        setStage(undefined);
        setEventType(undefined);
        setModality(undefined);
        setPartnerships(undefined);

        setSearchParams((prev) => {
            prev.delete("startDate");
            prev.delete("endDate");
            prev.delete("stage");
            prev.delete("eventType");
            prev.delete("modality");
            prev.delete("partnerships");
            return prev;
        });
    }, [setSearchParams]);

    return (
        <Card
            title={
                <div className="flex items-center gap-2">
                    <Filter size={20} className="text-blue-500" />
                    <span>Filtros de Eventos</span>
                </div>
            }
            className={`shadow-md mb-6 ${className || ""}`}
        >
            <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Rango de Fechas
                        </label>
                        <RangePicker
                            style={{ width: "100%" }}
                            placeholder={["Fecha inicio", "Fecha fin"]}
                            value={dateRange}
                            onChange={(dates) =>
                                setDateRange(dates as [Dayjs, Dayjs] | null)
                            }
                            presets={[
                                {
                                    label: "Este mes",
                                    value: [
                                        dayjs().startOf("month"),
                                        dayjs().endOf("month"),
                                    ],
                                },
                                {
                                    label: "Último mes",
                                    value: [
                                        dayjs().subtract(1, "month").startOf("month"),
                                        dayjs().subtract(1, "month").endOf("month"),
                                    ],
                                },
                                {
                                    label: "Este año",
                                    value: [
                                        dayjs().startOf("year"),
                                        dayjs().endOf("year"),
                                    ],
                                },
                            ]}
                        />
                    </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Etapa
                        </label>
                        <Select
                            style={{ width: "100%" }}
                            placeholder="Seleccionar etapa"
                            value={stage}
                            onChange={setStage}
                            mode="multiple"
                            allowClear
                        >
                            <Select.Option value="planning">
                                Planificación
                            </Select.Option>
                            <Select.Option value="launched">Lanzado</Select.Option>
                            <Select.Option value="enrollment_closed">
                                Inscripciones Cerradas
                            </Select.Option>
                            <Select.Option value="finished">Finalizado</Select.Option>
                        </Select>
                    </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Tipo de Evento
                        </label>
                        <Select
                            style={{ width: "100%" }}
                            placeholder="Seleccionar tipo"
                            value={eventType}
                            onChange={setEventType}
                            allowClear
                        >
                            <Select.Option value="general">General</Select.Option>
                            <Select.Option value="specific">Específico</Select.Option>
                        </Select>
                    </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Modalidad
                        </label>
                        <Select
                            style={{ width: "100%" }}
                            placeholder="Seleccionar modalidad"
                            value={modality}
                            onChange={setModality}
                            allowClear
                        >
                            <Select.Option value="online">Online</Select.Option>
                            <Select.Option value="presential">Presencial</Select.Option>
                            <Select.Option value="hybrid">Híbrido</Select.Option>
                        </Select>
                    </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Alianzas
                        </label>
                        <Select
                            style={{ width: "100%" }}
                            placeholder="Seleccionar alianza"
                            value={partnerships}
                            onChange={setPartnerships}
                            allowClear
                        >
                            <Select.Option value="active">Activas</Select.Option>
                            <Select.Option value="inactive">Inactivas</Select.Option>
                        </Select>
                    </div>
                </Col>

                <Col xs={24} sm={12} md={8} lg={6}>
                    <div className="flex gap-2 items-end h-full pb-1">
                        <Button type="primary" onClick={handleApplyFilters}>
                            Aplicar
                        </Button>
                        <Button
                            icon={<RotateCcw size={16} />}
                            onClick={handleClearFilters}
                        >
                            Limpiar
                        </Button>
                    </div>
                </Col>
            </Row>
        </Card>
    );
};

export default EventFilterCard;
