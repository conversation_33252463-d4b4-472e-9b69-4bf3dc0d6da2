import React from "react";
import { Row, Col } from "antd";
import type { DashboardEventQueryParams } from "@/features/crm/types/dashboard/events";
import { useDashboardEventsHistorical } from "@/features/crm/hooks/use-dashboard-events";
import EventsHistoricalChart from "@/features/crm/components/molecules/events/events-historical-chart";

interface EventsHistoricalSectionProps {
    queryParams: DashboardEventQueryParams;
}

const EventsHistoricalSection: React.FC<EventsHistoricalSectionProps> = ({
    queryParams,
}) => {
    const { data: historicalData, isLoading } =
        useDashboardEventsHistorical(queryParams);

    return (
        <div className="space-y-6">
            {/* Charts Row */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    <EventsHistoricalChart
                        data={historicalData}
                        isLoading={isLoading}
                    />
                </Col>
            </Row>
        </div>
    );
};

export default EventsHistoricalSection;
