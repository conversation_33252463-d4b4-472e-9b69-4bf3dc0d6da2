import {
    type OrderItemOffering,
    Order,
    OrderItem,
    OrderItemInvitationStatus,
    OrderStage,
} from "../../types/order";
import {
    Book,
    ExternalLink,
    Tag as TagIcon,
    RefreshCw,
    AlertCircle,
    CheckCircle,
    Loader,
} from "lucide-react";
import { Link } from "react-router-dom";
import { Toolt<PERSON>, Typography, Tag, Button, App } from "antd";
import { useSendClassroomInvitation } from "../../hooks/use-order-item";
import type { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

const { Text } = Typography;

type OrderItemOfferingCellProps = {
    content: OrderItemOffering;
    orderItem: OrderItem;
    orderData: Pick<Order, "oid" | "stage">;
};

export default function OrderItemOfferingCell({
    content,
    orderItem,
    orderData,
}: OrderItemOfferingCellProps) {
    const { message } = App.useApp();

    const { handleError } = useApiError({
        title: "Error al reenviar la invitación",
        genericMessage: "No se pudo reenviar la invitación al aula virtual"
    });

    const { mutate: sendInvitation, isPending: isSending } = useSendClassroomInvitation(
        {
            onSendInvitationSuccess: () => {
                message.success("Invitación reenviada correctamente");
            },
            onSendInvitationError: (error: AxiosError) => {
                handleError(error);
            },
        },
    );

    const handleResendInvitation = () => {
        if (content.oid) {
            sendInvitation({
                orderItemId: orderItem.oiid,
                oid: orderData.oid,
            });
        }
    };

    const getInvitationStatusIndicator = () => {
        if (orderData.stage !== OrderStage.SOLD) return null;

        const status = orderItem.extInvitationStatus;

        switch (status) {
            case OrderItemInvitationStatus.PENDING:
                return (
                    <Tooltip title="Invitación pendiente de envío">
                        <div className="w-2 h-2 rounded-full bg-yellow-500 animate-pulse" />
                    </Tooltip>
                );
            case OrderItemInvitationStatus.SENDING:
                return (
                    <Tooltip title="Enviando invitación...">
                        <Loader size={12} className="text-blue-500 animate-spin" />
                    </Tooltip>
                );
            case OrderItemInvitationStatus.SENT:
                return (
                    <Tooltip title="Invitación enviada correctamente">
                        <CheckCircle size={12} className="text-green-500" />
                    </Tooltip>
                );
            default: // defaul is error
                return (
                    <Tooltip
                        title={
                            <div className="space-y-2">
                                <div>Error al enviar la invitación a Classroom</div>
                                <Button
                                    size="small"
                                    type="primary"
                                    icon={<RefreshCw size={12} />}
                                    loading={isSending}
                                    onClick={handleResendInvitation}
                                >
                                    Reintentar
                                </Button>
                            </div>
                        }
                        trigger="click"
                    >
                        <AlertCircle
                            size={12}
                            className="text-red-500 cursor-pointer hover:text-red-600"
                        />
                    </Tooltip>
                );
        }
    };

    return (
        <>
            <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-2">
                    <Book size={16} className="text-blue-medium" strokeWidth={1.75} />
                    <Text
                        className="font-medium text-black-full line-clamp-1"
                        title={content.name}
                    >
                        {content.name}
                    </Text>
                    {getInvitationStatusIndicator()}
                </div>

                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                        <TagIcon
                            size={14}
                            className="text-black-medium"
                            strokeWidth={1.75}
                        />
                        <Tag
                            className="m-0 text-xs rounded-full"
                            bordered={false}
                            color="blue"
                        >
                            {content.slug}
                        </Tag>
                        <Tooltip title="Ver detalles del producto">
                            <Link
                                to={`/cms/offering/${content.oid}`}
                                className="flex items-center text-blue-full hover:text-blue-medium transition-colors"
                            >
                                <ExternalLink
                                    size={14}
                                    className="ml-2"
                                    strokeWidth={1.75}
                                />
                            </Link>
                        </Tooltip>
                    </div>
                </div>
            </div>
        </>
    );
}
