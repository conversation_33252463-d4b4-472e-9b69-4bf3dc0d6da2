import { PhoneNumber } from "antd-phone-input/types";

type AuditBaseType = {
    uid: string;
    key: string;
    createdAt: string;
    updatedAt: string;
};

export enum ContactOcupation {
    STUDENT = "student",
    EMPLOYEE = "employee",
    INDEPENDENT = "independent",
}

export const ContactOcupationLabel: Record<ContactOcupation, string> = {
    [ContactOcupation.STUDENT]: "Estudiante",
    [ContactOcupation.EMPLOYEE]: "Empleado",
    [ContactOcupation.INDEPENDENT]: "Independiente",
};

export type ProfilePhoto = {
    fid: string;
    name: string;
    url: string;
    contentType: string;
};

export type EducationalInstitution = {
    eiid: string;
    name: string;
    acronym: string;
};

export type Major = {
    mid: string;
    name: string;
};

export type Term = {
    tid: string;
    name: string;
};

type ContactBase = {
    username: string;
    fullName: string;
    isActive: boolean;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    ocupation: ContactOcupation;
    company?: string;
    role?: string;
    isStaff: boolean;
    profilePhoto?: ProfilePhoto;
    educationalInstitution?: EducationalInstitution;
    googleContactId?: string;
    lastGoogleSync?: string;
};

export type ContactListItem = ContactBase & {
    major?: string;
    term?: string;
    country?: string;
    university?: string;
} & AuditBaseType;

export type ContactRetrieve = ContactBase & {
    major?: Major;
    term?: Term;
    idNumber?: string;
    contactsWithSameIdNumber?: Pick<ContactListItem, "uid" | "phoneNumber">[];
} & AuditBaseType;

export type ContactCreateForm = {
    firstName?: string;
    lastName?: string;
    phoneNumber: PhoneNumber;
    email?: string;
    educationalInstitution?: string;
};

export type ContactCreateRequest = Omit<ContactCreateForm, "phoneNumber"> & {
    phoneNumber: string;
};

export type ContactUpdateForm = Omit<ContactCreateForm, "phoneNumber"> & {
    ocupation?: ContactOcupation;
    company?: string;
    role?: string;
    major?: string;
    term?: string;
    phoneNumber: string | PhoneNumber;
    idNumber?: string;
};

export type ContactUpdateRequest = Omit<ContactUpdateForm, "phoneNumber"> & {
    phoneNumber: string;
};
