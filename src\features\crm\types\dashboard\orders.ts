import type { Order, OrderStage } from "../order";

export type DashboardOrderTendency = "up" | "down" | "flat";

export type DashboardOrderQueryParams = Partial<{
    createdAtBefore: string;
    createdAtAfter: string;
    stages: string;
    products: string;
    salesAgent: string;
    forceRefresh: boolean;
    benefits: string;
}>;

/**
 * Rate types
 */
export type DashboardOrderStatsRate = {
    value: number;
    percentage: number;
    tendency: DashboardOrderTendency;
};

export type DashboardOrderStats = {
    totalSalesAmount: {
        pen: number;
        usd: number;
        total: number; // PEN + USD converted from USD to PEN
        currencyExchangeRate: number; // Exchange rate from USD to PEN
    };
    clients: number;
    conversionRate: DashboardOrderStatsRate;
    salesThisMonth: DashboardOrderStatsRate;
};

export type ConversionFunnelItem = {
    name: string;
    value: number;
    stage: OrderStage;
};

export type ConversionBySaleStagesItem = {
    percentage: number;
    fromCount: number;
    toCount: number;
};

export type ConversionBySaleStages = {
    prospectToInterested: ConversionBySaleStagesItem;
    interestedToPay: ConversionBySaleStagesItem;
    toPayToPaid: ConversionBySaleStagesItem;
    resume: {
        generalConversionRate: number;
        lost: number;
        closedSales: number;
    };
};

export type OrdersByMonthItem = {
    month: string;
    total: number;
    prospect: number;
    interested: number;
    toPay: number;
    sold: number;
    lost: number;
};

export type OrdersByStageItem = {
    name: string;
    value: number;
    stage: OrderStage;
};

export type RevenueByMonthItem = {
    month: string;
    revenue: number;
};

export type RevenueByAgent = {
    name: string;
    value: number;
    effectiveRevenue: number;
    agentUid: string;
    percentage: number;
};

export type ProductParticipation = {
    percentage: number;
    tendency: "up" | "down" | "flat";
    change: number;
};

export type TopSellingProduct = {
    name: string;
    count: number;
    revenue: number;
    effectiveRevenue: number;
    lostRevenue: number;
    debt: number;
    offeringOid: string;
    participation: ProductParticipation;
};

export type CurrentMonthPerformanceMetric = {
    current: number;
    previous: number;
    percentageChange: number;
    tendency: "up" | "down" | "flat";
};

export type SalesProgress = {
    totalRevenue: number;
    target: number;
};

export type CurrentMonthPerformance = {
    period: string;
    sales: CurrentMonthPerformanceMetric;
    orders: CurrentMonthPerformanceMetric;
    conversion: CurrentMonthPerformanceMetric;
    salesProgress: SalesProgress;
};

export type RecentOrderOwner = {
    uid: string;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string | null;
    phoneNumber: string;
};

export type RecentOrderSalesAgent = {
    uid: string;
    firstName: string;
    lastName: string;
    fullName: string;
};

export type FilterProduct = {
    oid: string;
    name: string;
    slug: string;
};

export type FilterSalesAgent = {
    uid: string;
    fullName: string;
};

export type FilterBenefit = {
    bid: string;
    name: string;
};

export type DashboardOrdersFilterOptions = {
    products: FilterProduct[];
    salesAgents: FilterSalesAgent[];
    benefits: FilterBenefit[];
};

export type WeeklyStageEvolution = {
    day: string;
    date: string;
    prospect: number;
    interested: number;
    toPay: number;
    sold: number;
    lost: number;
};

// Tipos para endpoints independientes
export type DashboardOrderSummaryData = {
    stats: DashboardOrderStats;
    conversionFunnel: ConversionFunnelItem[];
    ordersByStage: OrdersByStageItem[];
    weeklyStageEvolution: WeeklyStageEvolution[];
    filterOptions: DashboardOrdersFilterOptions;
};

export type DashboardOrderAgentsData = {
    revenueBySalesAgent: RevenueByAgent[];
};

export type DashboardOrderAnalyticsData = {
    conversionBySaleStages: ConversionBySaleStages;
    currentMonthPerformance: CurrentMonthPerformance;
};

export type DashboardOrderHistoricalData = {
    ordersByMonth: OrdersByMonthItem[];
    revenueByMonth: RevenueByMonthItem[];
};

export type DashboardOrderProductsData = {
    topSellingProducts: TopSellingProduct[];
};

export type DashboardOrderRecentOrdersData = {
    recentOrders: Order[];
};
